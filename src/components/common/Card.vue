<template>
  <view class="card" :style="{ borderRadius: borderRadius, padding: padding }">
    <view v-if="title" class="card-header flex justify-between items-center mb-20rpx">
      <view class="flex items-center">
        <view v-if="showTitleLine" class="title-line mr-10rpx" :style="{ backgroundColor: lineColor }"></view>
        <text class="text-32rpx font-bold" :style="{ color: titleColor }">{{ title }}</text>
      </view>
      <text v-if="subTitle" class="text-26rpx text-grey">{{ subTitle }}</text>
    </view>
    <view class="card-content">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: ''
  },
  subTitle: {
    type: String,
    default: ''
  },
  borderRadius: {
    type: String,
    default: '8rpx'
  },
  padding: {
    type: String,
    default: '20rpx'
  },
  showTitleLine: {
    type: Boolean,
    default: true
  },
  lineColor: {
    type: String,
    default: '#ff6d00'
  },
  titleColor: {
    type: String,
    default: '#333'
  }
})
</script>

<style lang="scss" scoped>
.card {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;

  .title-line {
    width: 6rpx;
    height: 32rpx;
    border-radius: 3rpx;
  }

  .card-content {
    width: 100%;
  }
}
</style> 