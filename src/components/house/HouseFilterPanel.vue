<template>
  <view class="filter-panel">
    <!-- 筛选菜单栏 -->
    <view class="filter-menu">
      <view
        v-for="(menu, index) in filterMenus"
        :key="index"
        class="filter-menu-item"
        :class="{ active: activeMenuIndex === index }"
        @tap="toggleMenu(index)"
      >
        <text class="menu-text">{{ menu.name }}</text>
        <text
          :class="[
            activeMenuIndex === index
              ? 'i-carbon-chevron-up'
              : 'i-carbon-chevron-down',
            'menu-icon',
          ]"
        ></text>
      </view>
    </view>

    <!-- 使用uni-popup组件 -->
    <uni-popup
      ref="popup"
      type="top"
      background-color="#fff"
      @change="handlePopupChange"
    >
      <view class="pb-120rpx">
        <!-- 区域筛选 -->
        <view v-if="activeMenuIndex === 0" class="popup-content">
          <view class="content-scroll">
            <view class="content-wrapper">
              <view class="section-title">区域选择</view>
              <view class="option-list">
                <view
                  v-for="(item, index) in areaOptions"
                  :key="index"
                  class="option-item"
                  :class="{ active: selectedFilters.area === item.value }"
                  @tap.stop="selectOption('area', item.value, item.text)"
                >
                  <text class="option-text">{{ item.text }}</text>
                  <view
                    class="option-indicator"
                    v-if="selectedFilters.area === item.value"
                  >
                    <text class="i-carbon-checkmark"></text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="filter-actions">
            <view class="reset-btn" @tap.stop="resetFilter('area')">
              <text class="i-carbon-reset mr-8rpx"></text>
              <text>重置</text>
            </view>
            <view class="confirm-btn" @tap.stop="applyFilters">确定</view>
          </view>
        </view>

        <!-- 租金筛选 -->
        <view v-else-if="activeMenuIndex === 1" class="popup-content">
          <view class="content-scroll">
            <view class="content-wrapper">
              <view class="section-title">价格区间</view>
              <!-- 价格选项 -->
              <view class="option-list">
                <view
                  v-for="(item, index) in priceOptions"
                  :key="index"
                  class="option-item"
                  :class="{ active: selectedFilters.price === item.value }"
                  @tap.stop="selectOption('price', item.value, item.text)"
                >
                  <text class="option-text">{{ item.text }}</text>
                  <view
                    class="option-indicator"
                    v-if="selectedFilters.price === item.value"
                  >
                    <text class="i-carbon-checkmark"></text>
                  </view>
                </view>
              </view>

              <!-- 价格滑块 -->
              <view class="price-slider">
                <view class="slider-range">
                  <text class="range-min">0</text>
                  <text class="range-max">20000以上</text>
                </view>
                <view class="slider-container">
                  <slider
                    :min="0"
                    :max="20000"
                    :value="priceRange[1]"
                    :show-value="false"
                    block-color="#FF6D00"
                    active-color="#FF6D00"
                    @change="onSliderChange"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="filter-actions">
            <view class="reset-btn" @tap.stop="resetFilter('price')">
              <text class="i-carbon-reset mr-8rpx"></text>
              <text>重置</text>
            </view>
            <view class="confirm-btn" @tap.stop="applyFilters">确定</view>
          </view>
        </view>

        <!-- 户型筛选 -->
        <view v-else-if="activeMenuIndex === 2" class="popup-content">
          <view class="content-scroll">
            <view class="content-wrapper">
              <view class="option-group">
                <view class="section-title">整租/合租</view>
                <view class="tag-list">
                  <view
                    class="tag-item"
                    :class="{ active: selectedFilters.rentType === '整租' }"
                    @tap.stop="selectOption('rentType', '整租', '整租')"
                  >
                    整租
                  </view>
                  <view
                    class="tag-item"
                    :class="{ active: selectedFilters.rentType === '合租' }"
                    @tap.stop="selectOption('rentType', '合租', '合租')"
                  >
                    合租
                  </view>
                </view>
              </view>

              <view class="option-group">
                <view class="section-title">户型</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in layoutOptions"
                    :key="index"
                    class="tag-item"
                    :class="{ active: selectedFilters.layout === item.value }"
                    @tap.stop="selectOption('layout', item.value, item.text)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="filter-actions">
            <view class="reset-btn" @tap.stop="resetLayoutFilters">
              <text class="i-carbon-reset mr-8rpx"></text>
              <text>重置</text>
            </view>
            <view class="confirm-btn" @tap.stop="applyFilters">确定</view>
          </view>
        </view>

        <!-- 更多筛选 -->
        <view v-else-if="activeMenuIndex === 3" class="popup-content">
          <view class="content-scroll">
            <view class="content-wrapper">
              <!-- 房型亮点 -->
              <view class="option-group">
                <view class="section-title">房型亮点</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in featureOptions"
                    :key="index"
                    class="tag-item"
                    :class="{ active: moreSelectedValues.includes(item.value) }"
                    @tap.stop="toggleMoreOption(item)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>

              <!-- 朝向 -->
              <view class="option-group">
                <view class="section-title">朝向</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in directionOptions"
                    :key="index"
                    class="tag-item"
                    :class="{ active: moreSelectedValues.includes(item.value) }"
                    @tap.stop="toggleMoreOption(item)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>

              <!-- 面积 -->
              <view class="option-group">
                <view class="section-title">面积</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in areaRangeOptions"
                    :key="index"
                    class="tag-item"
                    :class="{ active: moreSelectedValues.includes(item.value) }"
                    @tap.stop="toggleMoreOption(item)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="filter-actions">
            <view class="reset-btn" @tap.stop="resetMoreFilters">
              <text class="i-carbon-reset mr-8rpx"></text>
              <text>重置</text>
            </view>
            <view class="confirm-btn" @tap.stop="confirmMoreFilters">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";

// 定义组件属性
const props = defineProps({
  // 初始筛选条件
  initialFilters: {
    type: Object,
    default: () => ({}),
  },
});

// 定义事件
const emit = defineEmits(["filter-change", "filter-reset", "menuOpened"]);

// 筛选菜单
const filterMenus = reactive([
  { name: "位置", value: "area" },
  { name: "租金", value: "price" },
  { name: "整·不限", value: "layout" },
  { name: "更多", value: "more" },
]);

// 当前激活的菜单索引
const activeMenuIndex = ref(-1);

// popup引用
const popup = ref(null);

// 价格范围
const priceRange = ref([0, 20000]);

// 已选择的筛选条件
const selectedFilters = reactive({
  area: "",
  price: "",
  layout: "",
  rentType: "",
  more: [],
});

// 更多筛选选中的值
const moreSelectedValues = ref([]);
const moreSelectedLabels = ref([]);

// 区域选项
const areaOptions = reactive([
  { text: "不限", value: "" },
  { text: "海淀区", value: "haidian" },
  { text: "朝阳区", value: "chaoyang" },
  { text: "西城区", value: "xicheng" },
  { text: "东城区", value: "dongcheng" },
  { text: "丰台区", value: "fengtai" },
  { text: "石景山区", value: "shijingshan" },
  { text: "通州区", value: "tongzhou" },
  { text: "昌平区", value: "changping" },
  { text: "大兴区", value: "daxing" },
  { text: "顺义区", value: "shunyi" },
]);

// 租金选项
const priceOptions = reactive([
  { text: "不限", value: "" },
  { text: "≤2000元", value: "0-2000" },
  { text: "2000-3000元", value: "2000-3000" },
  { text: "3000-4000元", value: "3000-4000" },
  { text: "4000-5000元", value: "4000-5000" },
  { text: "5000-6000元", value: "5000-6000" },
  { text: "6000-8000元", value: "6000-8000" },
  { text: "8000-20000元", value: "8000-20000" },
  { text: "≥20000元", value: "20000-" },
]);

// 户型选项
const layoutOptions = reactive([
  { text: "不限", value: "" },
  { text: "1室", value: "1" },
  { text: "2室", value: "2" },
  { text: "3室", value: "3" },
  { text: "4室", value: "4" },
  { text: "5室+", value: "5+" },
]);

// 房型亮点选项
const featureOptions = reactive([
  { text: "双卫生间", value: "double-toilet" },
  { text: "loft/复式", value: "loft" },
  { text: "不看开间", value: "no-open" },
  { text: "开间", value: "open" },
]);

// 朝向选项
const directionOptions = reactive([
  { text: "东", value: "east" },
  { text: "西", value: "west" },
  { text: "南", value: "south" },
  { text: "北", value: "north" },
  { text: "南北", value: "south-north" },
]);

// 面积范围选项
const areaRangeOptions = reactive([
  { text: "≤40㎡", value: "0-40" },
  { text: "40-60㎡", value: "40-60" },
  { text: "60-80㎡", value: "60-80" },
  { text: "80-100㎡", value: "80-100" },
  { text: "100-120㎡", value: "100-120" },
  { text: "≥120㎡", value: "120-" },
]);

// 处理popup状态变化
const handlePopupChange = (e) => {
  if (!e.show) {
    closeMenu();
  }
};

// 切换菜单
const toggleMenu = (index: number) => {
  if (activeMenuIndex.value === index) {
    // 如果点击当前激活的菜单，则关闭
    closeMenu();
  } else {
    // 切换到对应的菜单
    activeMenuIndex.value = index;

    // 如果是更多选项，初始化选中的值
    if (index === 3) {
      moreSelectedValues.value = [...selectedFilters.more];
      moreSelectedLabels.value = selectedFilters.more.map((value) => {
        // 查找对应的标签
        const allOptions = [
          ...featureOptions,
          ...directionOptions,
          ...areaRangeOptions,
        ];
        const option = allOptions.find((opt) => opt.value === value);
        return option ? option.text : value;
      });
    }

    // 打开弹出层
    nextTick(() => {
      popup.value.open();
      emit("menuOpened");
    });
  }
};

// 关闭菜单
const closeMenu = () => {
  activeMenuIndex.value = -1;
  popup.value && popup.value.close();
};

// 选择选项
const selectOption = (type: string, value: string, label: string) => {
  selectedFilters[type] = value;

  // 更新菜单名称
  if (type === "rentType") {
    filterMenus[2].name = value || "整·不限";
  } else if (type === "area") {
    filterMenus[0].name = label || "位置";
  } else if (type === "price") {
    filterMenus[1].name = label || "租金";
  } else if (type === "layout") {
    if (selectedFilters.rentType) {
      filterMenus[2].name = `${selectedFilters.rentType}·${value || "不限"}`;
    } else {
      filterMenus[2].name = value ? `${value}室` : "整·不限";
    }
  }
};

// 滑块变化
const onSliderChange = (e) => {
  priceRange.value[1] = e.detail.value;

  // 根据滑块值设置价格范围
  if (priceRange.value[1] === 0) {
    selectOption("price", "", "不限");
  } else if (priceRange.value[1] <= 2000) {
    selectOption("price", "0-2000", "≤2000元");
  } else if (priceRange.value[1] <= 3000) {
    selectOption("price", "2000-3000", "2000-3000元");
  } else if (priceRange.value[1] <= 4000) {
    selectOption("price", "3000-4000", "3000-4000元");
  } else if (priceRange.value[1] <= 5000) {
    selectOption("price", "4000-5000", "4000-5000元");
  } else if (priceRange.value[1] <= 6000) {
    selectOption("price", "5000-6000", "5000-6000元");
  } else if (priceRange.value[1] <= 8000) {
    selectOption("price", "6000-8000", "6000-8000元");
  } else if (priceRange.value[1] <= 20000) {
    selectOption("price", "8000-20000", "8000-20000元");
  } else {
    selectOption("price", "20000-", "≥20000元");
  }
};

// 切换更多选项
const toggleMoreOption = (option) => {
  const index = moreSelectedValues.value.indexOf(option.value);

  if (index > -1) {
    // 如果已选中，则取消选中
    moreSelectedValues.value.splice(index, 1);
    moreSelectedLabels.value.splice(index, 1);
  } else {
    // 如果未选中，则添加选中
    moreSelectedValues.value.push(option.value);
    moreSelectedLabels.value.push(option.text);
  }
};

// 重置特定筛选条件
const resetFilter = (type: string) => {
  selectedFilters[type] = "";

  // 更新菜单名称
  if (type === "area") {
    filterMenus[0].name = "位置";
  } else if (type === "price") {
    filterMenus[1].name = "租金";
    priceRange.value = [0, 20000];
  }
};

// 重置户型筛选
const resetLayoutFilters = () => {
  selectedFilters.layout = "";
  selectedFilters.rentType = "";
  filterMenus[2].name = "整·不限";
};

// 重置更多筛选
const resetMoreFilters = () => {
  moreSelectedValues.value = [];
  moreSelectedLabels.value = [];
};

// 确认更多筛选
const confirmMoreFilters = () => {
  selectedFilters.more = [...moreSelectedValues.value];

  // 更新菜单名称
  if (moreSelectedValues.value.length > 0) {
    filterMenus[3].name = `更多(${moreSelectedValues.value.length})`;
  } else {
    filterMenus[3].name = "更多";
  }

  closeMenu();
  emit("filter-change", { ...selectedFilters });
};

// 应用筛选条件
const applyFilters = () => {
  closeMenu();
  emit("filter-change", { ...selectedFilters });
};

// 重置所有筛选条件
const resetFilters = () => {
  // 重置选中的筛选条件
  Object.keys(selectedFilters).forEach((key) => {
    if (key === "more") {
      selectedFilters[key] = [];
    } else {
      selectedFilters[key] = "";
    }
  });

  // 重置更多筛选选中的值
  moreSelectedValues.value = [];
  moreSelectedLabels.value = [];

  // 重置菜单名称
  filterMenus[0].name = "位置";
  filterMenus[1].name = "租金";
  filterMenus[2].name = "整·不限";
  filterMenus[3].name = "更多";

  // 发送重置事件
  emit("filter-reset");
};

// 初始化
onMounted(() => {
  // 设置初始筛选条件
  if (props.initialFilters) {
    Object.keys(props.initialFilters).forEach((key) => {
      if (selectedFilters.hasOwnProperty(key)) {
        selectedFilters[key] = props.initialFilters[key];
      }
    });
  }
});

// 暴露方法
defineExpose({
  resetFilters,
  closeMenu,
  activeMenuIndex,
});
</script>

<style lang="scss" scoped>
.filter-panel {
  width: 100%;
  z-index: 100;
  position: relative;
  background-color: #fff;
}

.filter-menu {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 6rpx;
    background: linear-gradient(
      to right,
      rgba(255, 109, 0, 0.05),
      rgba(255, 141, 66, 0.1)
    );
  }
}

.filter-menu-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.25s ease;

  &.active {
    color: #ff6d00;
    font-weight: 500;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 36rpx;
      height: 6rpx;
      background: linear-gradient(to right, #ff6d00, #ff8d42);
      border-radius: 3rpx;
    }
  }
}

.menu-text {
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-icon {
  margin-left: 4rpx;
  font-size: 24rpx;
}

.popup-content {
  position: relative;
  display: flex;
  flex-direction: column;
  max-height: 85vh;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
  overflow: hidden;
}

.content-scroll {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  max-height: calc(85vh - 120rpx);
}

.content-wrapper {
  padding: 24rpx 0;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin: 0 30rpx 24rpx;
  position: relative;
  padding-left: 20rpx;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 8rpx;
    bottom: 8rpx;
    width: 6rpx;
    background: linear-gradient(to bottom, #ff6d00, #ff8d42);
    border-radius: 3rpx;
  }
}

.option-list {
  padding: 0;
  margin-bottom: 24rpx;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 96rpx;
  font-size: 28rpx;
  color: #333;
  transition: all 0.2s ease;
  padding: 0 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);

  &:last-child {
    border-bottom: none;
  }

  &.active {
    color: #ff6d00;
    font-weight: 500;
    background-color: rgba(255, 109, 0, 0.05);

    .option-indicator {
      color: #fff;
      background: linear-gradient(135deg, #ff6d00, #ff8d42);
      box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.3);
    }
  }
}

.option-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: transparent;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.price-slider {
  padding: 30rpx;
  margin-top: 20rpx;
  background-color: rgba(255, 109, 0, 0.03);
  border-radius: 16rpx;
  margin: 0 30rpx 30rpx;
  border: 1rpx solid rgba(255, 109, 0, 0.08);
}

.slider-range {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.slider-container {
  padding: 0 10rpx;
}

.option-group {
  padding: 20rpx 0;
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 32rpx;
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
}

.tag-item {
  margin: 10rpx;
  padding: 0 30rpx;
  height: 68rpx;
  line-height: 68rpx;
  border-radius: 34rpx;
  font-size: 26rpx;
  color: #333;
  background-color: #f5f5f5;
  text-align: center;
  transition: all 0.3s ease;
  border: 1rpx solid transparent;

  &.active {
    color: #fff;
    background: linear-gradient(135deg, #ff6d00, #ff8d42);
    box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.2);
    transform: translateY(-2rpx);
    border: none;
  }

  &:active {
    transform: scale(0.98);
  }
}

.filter-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  background-color: #fff;
  z-index: 2;
  height: 100rpx;
  box-sizing: border-box;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.reset-btn {
  color: #666;
  background-color: #f5f5f5;
  margin-right: 20rpx;

  &:active {
    background-color: #e5e5e5;
    transform: scale(0.98);
  }
}

.confirm-btn {
  color: #fff;
  background: linear-gradient(to right, #ff6d00, #ff8d42);
  box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.2);

  &:active {
    box-shadow: 0 2rpx 6rpx rgba(255, 109, 0, 0.2);
    transform: translateY(2rpx);
  }
}

.mr-8rpx {
  margin-right: 8rpx;
}

.pb-120rpx {
  padding-bottom: 120rpx;
}
</style>
