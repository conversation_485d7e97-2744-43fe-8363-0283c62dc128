<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
onLaunch(() => {
  console.log("App Launch");
});
onShow(() => {
  console.log("App Show");
});
onHide(() => {
  console.log("App Hide");
});
</script>

<style>
/* 全局样式 */
@import "./styles/app.css";
</style>

<style lang="scss">
/* 第三方 UI 库 */
@import "@climblee/uv-ui/index.scss";

::v-deep .uni-nav-bar-text {
  font-size: 34rpx !important;
  font-weight: 500 !important;
  color: #212529;
}

::v-deep .uni-icons {
  font-size: 48rpx !important;
}
</style>
