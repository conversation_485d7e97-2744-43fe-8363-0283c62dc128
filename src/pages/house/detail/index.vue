<template>
  <view class="container">
    <!-- 沉浸式导航栏 -->
    <uni-nav-bar
      fixed
      :border="false"
      status-bar
      :title="showNavTitle ? '房源详情' : ''"
      left-icon="left"
      :background-color="navBgColor"
      :color="navTextColor"
      @clickLeft="goBack"
    />

    <!-- 内容区域 -->
    <scroll-view
      scroll-y
      enable-flex
      class="content-scroll"
      @scroll="handleScroll"
      :scroll-top="scrollTop"
    >
      <!-- 轮播图 -->
      <swiper
        class="image-swiper"
        :indicator-dots="true"
        :autoplay="false"
        :interval="3000"
        :duration="500"
        :circular="true"
        indicator-color="rgba(255, 255, 255, 0.6)"
        indicator-active-color="#FFFFFF"
      >
        <swiper-item
          v-for="(image, index) in houseDetail.images"
          :key="index"
          @tap="previewImages(index)"
        >
          <image :src="image" mode="aspectFill" class="swiper-image" />
        </swiper-item>
      </swiper>

      <!-- 图片计数 -->
      <view class="image-counter">
        <text>{{ currentImageIndex + 1 }}/{{ houseDetail.images.length }}</text>
      </view>

      <!-- VR标签 -->
      <!-- <view v-if="houseDetail.hasVR" class="vr-tag">
        <text class="i-carbon-view text-20rpx mr-6rpx"></text>
        <text>VR看房</text>
      </view> -->

      <!-- 基本信息卡片 -->
      <view class="content">
        <view class="info-card">
          <!-- 价格和标签 -->
          <view class="price-section">
            <view class="price-info">
              <text class="price">{{ houseDetail.price }}</text>
              <text class="price-unit">元/月</text>
            </view>
            <view class="payment-method">{{
              houseDetail.paymentMethod || "押一付三"
            }}</view>
          </view>

          <!-- 标题 -->
          <view class="title-section">
            <text class="house-title">{{ formatTitle() }}</text>
          </view>

          <!-- 基本属性 -->
          <view class="attributes">
            <view class="attribute-item">
              <text class="attribute-value">{{ houseDetail.layout }}</text>
              <text class="attribute-label">户型</text>
            </view>
            <view class="attribute-item">
              <text class="attribute-value">{{ houseDetail.area }}㎡</text>
              <text class="attribute-label">面积</text>
            </view>
            <view class="attribute-item">
              <text class="attribute-value">{{ houseDetail.direction }}</text>
              <text class="attribute-label">朝向</text>
            </view>
            <view class="attribute-item">
              <text class="attribute-value">{{ houseDetail.floor }}</text>
              <text class="attribute-label">楼层</text>
            </view>
          </view>

          <!-- 房源标签 -->
          <view class="tags-section">
            <text
              v-for="(tag, index) in houseDetail.tags"
              :key="index"
              class="tag-item"
              >{{ tag }}</text
            >
          </view>
        </view>

        <!-- 位置信息 -->
        <view class="section-card">
          <view class="section-header">
            <text class="section-title">小区位置</text>
          </view>
          <view class="location-content">
            <view class="location-info">
              <view class="community-name">{{ houseDetail.community }}</view>
              <view class="address">
                <text class="i-carbon-location color-grey mr-10rpx"></text>
                <text class="address-text">{{ houseDetail.address }}</text>
              </view>
            </view>
            <view class="map-container" @tap="openMap">
              <image
                src="/static/images/map-placeholder.png"
                mode="aspectFill"
                class="map-image"
              />
              <view class="map-overlay">
                <text class="i-carbon-map text-36rpx"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 房源描述 -->
        <view class="section-card">
          <view class="section-header">
            <text class="section-title">房源描述</text>
          </view>
          <view class="description-content">
            <text class="description-text">{{ houseDetail.description }}</text>
            <view
              class="show-more"
              v-if="isDescriptionFolded && houseDetail.description.length > 100"
              @tap="toggleDescription"
            >
              <text class="show-more-text">展开</text>
              <text class="i-carbon-chevron-down text-24rpx ml-4rpx"></text>
            </view>
            <view
              class="show-more"
              v-if="
                !isDescriptionFolded && houseDetail.description.length > 100
              "
              @tap="toggleDescription"
            >
              <text class="show-more-text">收起</text>
              <text class="i-carbon-chevron-up text-24rpx ml-4rpx"></text>
            </view>
          </view>
        </view>

        <!-- 配套设施 -->
        <!-- <view class="section-card">
          <view class="section-header">
            <text class="section-title">配套设施</text>
          </view>
          <view class="facilities-content">
            <view
              v-for="(facility, index) in houseDetail.facilities"
              :key="index"
              class="facility-item"
            >
              <text :class="['facility-icon', facility.icon]"></text>
              <text class="facility-name">{{ facility.name }}</text>
            </view>
          </view>
        </view> -->

        <view class="section-card">
          <view class="section-header">
            <text class="section-title">配套设施</text>
          </view>
          <view class="facilities-content">
            <PropertyTag
              v-for="(facility, index) in houseDetail.facilities"
              :key="index"
              :text="facility.name"
              :type="
                index % 3 === 0
                  ? 'default'
                  : index % 3 === 1
                  ? 'info'
                  : 'success'
              "
              :iconClass="facility.icon || 'i-carbon-checkmark'"
            />
          </view>
        </view>

        <!-- 房东信息 -->
        <view class="section-card">
          <view class="section-header">
            <text class="section-title">房东信息</text>
          </view>
          <view class="landlord-content">
            <view class="landlord-info">
              <image
                :src="houseDetail.landlord.avatar"
                mode="aspectFill"
                class="landlord-avatar"
              />
              <view class="landlord-details">
                <view class="landlord-name">{{
                  houseDetail.landlord.name
                }}</view>
                <view class="landlord-type">{{
                  houseDetail.landlord.type
                }}</view>
              </view>
            </view>
            <view class="landlord-stats">
              <view class="stat-item">
                <text class="stat-value">{{
                  houseDetail.landlord.responseRate
                }}</text>
                <text class="stat-label">回复率</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">{{
                  houseDetail.landlord.responseTime
                }}</text>
                <text class="stat-label">回复时间</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 推荐房源 -->
        <view class="section-card">
          <view class="section-header">
            <text class="section-title">相似房源推荐</text>
            <view class="more-link" @tap="navigateToList">
              <text class="more-text">查看更多</text>
              <text class="i-carbon-arrow-right text-24rpx ml-4rpx"></text>
            </view>
          </view>
          <scroll-view
            scroll-x
            class="similar-houses-scroll"
            show-scrollbar="false"
          >
            <view class="similar-houses-content">
              <view
                v-for="(house, index) in similarHouses"
                :key="index"
                class="similar-house-card"
                @tap="navigateToDetail(house.id)"
              >
                <image
                  :src="house.image"
                  mode="aspectFill"
                  class="similar-house-image"
                />
                <view class="similar-house-info">
                  <text class="similar-house-title">{{ house.title }}</text>
                  <text class="similar-house-details"
                    >{{ house.layout }} | {{ house.area }}㎡</text
                  >
                  <view class="similar-house-price">
                    <text class="similar-price-value">{{ house.price }}</text>
                    <text class="similar-price-unit">元/月</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="h-160rpx"> </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-buttons">
        <view class="action-button" @tap="toggleCollect">
          <text
            :class="[
              'action-icon',
              isCollected
                ? 'i-carbon-star-filled text-primary'
                : 'i-carbon-star color-grey',
            ]"
          ></text>
          <text class="action-text" :class="{ 'text-primary': isCollected }">{{
            isCollected ? "已收藏" : "收藏"
          }}</text>
        </view>
        <view class="action-button" @tap="shareHouse">
          <text class="action-icon i-carbon-share color-grey"></text>
          <text class="action-text">分享</text>
        </view>
      </view>
      <view class="contact-buttons">
        <view class="contact-button phone-button" @tap="callPhone">
          <text class="i-carbon-phone mr-10rpx"></text>
          <text>电话联系</text>
        </view>
        <view class="contact-button message-button" @tap="sendMessage">
          <text class="i-carbon-chat mr-10rpx"></text>
          <text>在线咨询</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import PropertyTag from "@/components/PropertyTag.vue";

// 获取页面参数
const houseId = ref("");
const currentImageIndex = ref(0);
const isDescriptionFolded = ref(true);
const isCollected = ref(false);
const scrollTop = ref(0);

// 导航栏状态
const showNavTitle = ref(false);
const navBgColor = ref("rgba(255, 255, 255, 0)");
const navTextColor = ref("#ffffff");

// 处理页面滚动
const handleScroll = (e: any) => {
  const scrollTop = e.detail.scrollTop;
  // 滚动距离超过轮播图高度的一半时，显示导航栏背景和标题
  if (scrollTop > 120) {
    navBgColor.value = "#ffffff";
    navTextColor.value = "#000000";
    showNavTitle.value = true;
  } else {
    // 计算透明度，实现渐变效果
    const opacity = Math.min(scrollTop / 100, 1).toFixed(2);
    navBgColor.value = `rgba(255, 255, 255, ${opacity})`;
    navTextColor.value = scrollTop > 100 ? "#000000" : "#ffffff";
    showNavTitle.value = scrollTop > 100;
  }
};

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    houseId.value = options.id;
    console.log("获取到房源ID:", houseId.value);
    // 实际项目中应该根据ID从服务器获取房源详情
    // 此处使用模拟数据
  }
});

// 模拟房源详情数据
const houseDetail = ref({
  id: "1",
  title: "整租2居 · 香山南营66号",
  community: "香山南营66号",
  layout: "2室1厅1卫",
  area: "70",
  floor: "高楼层",
  direction: "南北",
  price: "4600",
  address: "北京市海淀区香山南路66号",
  location: "西山",
  tags: ["南北通透", "高楼层", "配套齐全", "采光好", "交通便利"],
  images: [
    "https://picsum.photos/seed/house1/750/500",
    "https://picsum.photos/seed/house2/750/500",
    "https://picsum.photos/seed/house3/750/500",
    "https://picsum.photos/seed/house4/750/500",
    "https://picsum.photos/seed/house5/750/500",
  ],
  hasVR: true,
  isNew: false,
  paymentMethod: "押一付一",
  utilities: "民水民电",
  rentType: "整租",
  description:
    "该房源位于香山南营小区，南北通透，采光充足。小区环境优美，周边配套齐全，距离地铁站步行仅需10分钟。房间干净整洁，家具家电齐全，可拎包入住。适合小家庭或白领租住。房源特点：1. 南北通透，采光好；2. 配套齐全，拎包入住；3. 交通便利，距离地铁站近；4. 小区环境好，安静舒适；5. 周边商超、医院、学校齐全。",
  facilities: [
    { name: "床", icon: "i-solar-bed-outline" },
    { name: "空调", icon: "i-solar-air-conditioner" },
    { name: "洗衣机", icon: "i-solar-washing-machine-outline" },
    { name: "冰箱", icon: "i-solar-refrigerator-outline" },
    { name: "热水器", icon: "i-solar-heat" },
    { name: "电视", icon: "i-carbon-tv" },
    { name: "衣柜", icon: "i-carbon-closet" },
    { name: "沙发", icon: "i-carbon-sofa" },
    { name: "阳台", icon: "i-carbon-balcony" },
    { name: "宽带", icon: "i-carbon-wifi" },
    { name: "燃气", icon: "i-carbon-gas" },
    { name: "暖气", icon: "i-carbon-warm" },
  ],
  landlord: {
    name: "张先生",
    avatar: "https://picsum.photos/seed/landlord1/100/100",
    type: "个人房东",
    responseRate: "98%",
    responseTime: "1小时内",
  },
});

// 相似房源推荐
const similarHouses = ref([
  {
    id: "2",
    title: "整租2居 · 远洋天地",
    layout: "2室1厅1卫",
    area: "75",
    price: "5200",
    image: "https://picsum.photos/seed/similar1/300/200",
  },
  {
    id: "3",
    title: "整租3居 · 万科星园",
    layout: "3室2厅1卫",
    area: "90",
    price: "6800",
    image: "https://picsum.photos/seed/similar2/300/200",
  },
  {
    id: "4",
    title: "整租1居 · 金茂府",
    layout: "1室1厅1卫",
    area: "45",
    price: "3500",
    image: "https://picsum.photos/seed/similar3/300/200",
  },
]);

// 格式化标题
const formatTitle = () => {
  const { rentType, layout, community } = houseDetail.value;
  const rooms = layout.match(/(\d+)室/) ? layout.match(/(\d+)室/)![1] : "";
  return `${rentType}${rooms}居 · ${community}`;
};

// 预览图片
const previewImages = (index: number) => {
  currentImageIndex.value = index;
  uni.previewImage({
    current: houseDetail.value.images[index],
    urls: houseDetail.value.images,
  });
};

// 展开/收起描述
const toggleDescription = () => {
  isDescriptionFolded.value = !isDescriptionFolded.value;
};

// 打开地图
const openMap = () => {
  uni.navigateTo({
    url: `/pages/house/map?id=${houseId.value}&community=${encodeURIComponent(
      houseDetail.value.community
    )}&address=${encodeURIComponent(houseDetail.value.address)}`,
  });
};

// 收藏房源
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({
    title: isCollected.value ? "已收藏" : "已取消收藏",
    icon: "none",
  });
};

// 分享房源
const shareHouse = () => {
  uni.showActionSheet({
    itemList: ["分享给朋友", "生成分享图片", "复制链接"],
    success: function (res) {
      uni.showToast({
        title: "分享功能开发中",
        icon: "none",
      });
    },
  });
};

// 电话联系
const callPhone = () => {
  uni.showModal({
    title: "联系房东",
    content: "确定要拨打房东电话吗？",
    success: function (res) {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: "13888888888", // 实际应从房源数据中获取
          fail: function () {
            uni.showToast({
              title: "拨号失败，请手动拨打",
              icon: "none",
            });
          },
        });
      }
    },
  });
};

// 在线咨询
const sendMessage = () => {
  uni.navigateTo({
    url: `/pages/message/chat?targetId=${houseDetail.value.landlord.name}&targetType=landlord&houseId=${houseId.value}`,
  });
};

// 前往房源列表
const navigateToList = () => {
  uni.navigateTo({
    url: "/pages/house/rent/list",
  });
};

// 前往其他房源详情
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/detail/index?id=${id}`,
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  position: relative;
  overflow-y: scroll;
}

.content-scroll {
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;
}

/* 轮播图区域 */
.image-swiper {
  width: 100%;
  height: 600rpx;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.image-counter {
  position: absolute;
  right: 30rpx;
  top: calc(44px + env(safe-area-inset-top) + 20rpx);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}

.vr-tag {
  position: absolute;
  left: 30rpx;
  top: calc(44px + env(safe-area-inset-top) + 20rpx);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
  display: flex;
  align-items: center;
}

.content {
  position: relative;
  top: -50rpx;
}
/* 信息卡片 */
.info-card {
  margin-top: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6d00;
}

.price-unit {
  font-size: 28rpx;
  color: #ff6d00;
  margin-left: 6rpx;
}

.payment-method {
  font-size: 28rpx;
  color: #666;
  background-color: #f8f8f8;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

.title-section {
  margin-bottom: 30rpx;
}

.house-title {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
}

.attributes {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.attribute-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.attribute-value {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.attribute-label {
  font-size: 24rpx;
  color: #999;
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

/* 内容区域卡片 */
.section-card {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
}

.more-link {
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 26rpx;
  color: #999;
}

/* 位置信息 */
.location-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-info {
  flex: 1;
}

.community-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.address {
  display: flex;
  align-items: center;
}

.address-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.map-container {
  width: 200rpx;
  height: 140rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  margin-left: 20rpx;
}

.map-image {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

/* 房源描述 */
.description-content {
  position: relative;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  text-align: justify;
  max-height: v-bind('isDescriptionFolded ? "200rpx" : "auto"');
  overflow: v-bind('isDescriptionFolded ? "hidden" : "visible"');
}

.show-more {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}

.show-more-text {
  font-size: 26rpx;
  color: #ff6d00;
}

/* 配套设施 */
.facilities-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.facility-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.facility-icon {
  font-size: 44rpx;
  color: #ff6d00;
  margin-bottom: 10rpx;
}

.facility-name {
  font-size: 24rpx;
  color: #666;
}

/* 房东信息 */
.landlord-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.landlord-info {
  display: flex;
  align-items: center;
}

.landlord-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.landlord-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.landlord-type {
  font-size: 24rpx;
  color: #999;
}

.landlord-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #999;
}

/* 相似房源 */
.similar-houses-scroll {
  width: 100%;
}

.similar-houses-content {
  display: flex;
  padding: 10rpx 0;
}

.similar-house-card {
  width: 280rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f9f9f9;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.similar-house-image {
  width: 100%;
  height: 180rpx;
}

.similar-house-info {
  padding: 16rpx;
}

.similar-house-title {
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.similar-house-details {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.similar-house-price {
  display: flex;
  align-items: baseline;
}

.similar-price-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #ff6d00;
}

.similar-price-unit {
  font-size: 22rpx;
  color: #ff6d00;
  margin-left: 4rpx;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 160rpx;
  padding: 20rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.1);
  padding-bottom: env(safe-area-inset-bottom);
}

.action-buttons {
  display: flex;
  width: 30%;
  height: 100%;
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 6rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

.contact-buttons {
  display: flex;
  width: 70%;
  height: 100%;
}

.contact-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  margin: 0 10rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.phone-button {
  background-color: rgba(255, 109, 0, 0.1);
  color: #ff6d00;
  border: 1px solid #ff6d00;
}

.message-button {
  background-color: #ff6d00;
  color: white;
}

/* 通用样式 */
.text-primary {
  color: #ff6d00;
}

.color-grey {
  color: #999;
}

.mr-4rpx {
  margin-right: 4rpx;
}

.mr-6rpx {
  margin-right: 6rpx;
}

.mr-10rpx {
  margin-right: 10rpx;
}

.ml-4rpx {
  margin-left: 4rpx;
}
</style>
