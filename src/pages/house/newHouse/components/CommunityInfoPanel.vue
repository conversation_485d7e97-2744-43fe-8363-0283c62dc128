<template>
  <view class="community-info-panel">
    <!-- 信息项列表 -->
    <view
      class="info-item"
      v-for="(item, index) in formattedInfoList"
      :key="index"
    >
      <view class="info-label text-999">{{ item.label }}</view>
      <view class="info-value">
        <text class="info-text">{{ item.value || "暂无数据" }}</text>
        <text v-if="item.hint" class="info-hint i-carbon-information"></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

// 定义组件属性
interface Props {
  communityInfo: {
    landArea?: string; // 占地面积
    buildingArea?: string; // 建筑面积
    greenArea?: string; // 绿化面积
    greenRate?: string; // 绿化率
    plotRatio?: string; // 容积率
    parkingSpaces?: string; // 规划车位
    parkingRatio?: string; // 车位配比
    buildingCount?: string; // 规划楼栋
    houseCount?: string; // 规划户型
    propertyCompany?: string; // 物业公司
    propertyFee?: string; // 物业费用
    heatingMode?: string; // 供暖方式
    waterSupply?: string; // 供水
  };
}

const props = defineProps<Props>();

// 格式化信息列表，用于展示
const formattedInfoList = computed(() => {
  const { communityInfo } = props;

  return [
    { label: "占地面积", value: communityInfo.landArea, type: "text" },
    { label: "建筑面积", value: communityInfo.buildingArea, type: "text" },
    { label: "绿化面积", value: communityInfo.greenArea, type: "text" },
    {
      label: "绿化率",
      value: communityInfo.greenRate,
      type: "text",
      hint: true,
    },
    {
      label: "容积率",
      value: communityInfo.plotRatio,
      type: "text",
      hint: true,
    },
    { label: "规划车位", value: communityInfo.parkingSpaces, type: "text" },
    {
      label: "车位配比",
      value: communityInfo.parkingRatio,
      type: "text",
      hint: true,
    },
    { label: "规划楼栋", value: communityInfo.buildingCount, type: "text" },
    { label: "规划户型", value: communityInfo.houseCount, type: "text" },
    { label: "物业公司", value: communityInfo.propertyCompany, type: "text" },
    {
      label: "物业费用",
      value: communityInfo.propertyFee,
      type: "text",
      hint: true,
    },
    {
      label: "供暖方式",
      value: communityInfo.heatingMode,
      type: "text",
      hint: true,
    },
    {
      label: "供水",
      value: communityInfo.waterSupply,
      type: "text",
      hint: true,
    },
  ].filter((item) => item.value !== undefined && item.value !== null);
});
</script>

<style lang="scss" scoped>
.community-info-panel {
  .info-item {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin: 14rpx 0;

    .info-label {
      width: 180rpx;
      flex-shrink: 0;
      padding-right: 24rpx;
      line-height: 1.5;
    }

    .info-value {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
      line-height: 1.5;

      .info-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .info-hint {
        margin-left: 12rpx;
        color: #aaa;
        font-size: 28rpx;
      }
    }
  }
}
</style>
