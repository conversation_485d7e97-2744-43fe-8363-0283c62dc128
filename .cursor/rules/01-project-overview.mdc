---
description: 
globs: 
alwaysApply: true
---
# 项目概述

这是一个基于uni-app框架的跨平台应用项目，使用Vue 3和TypeScript开发。该项目名为"本地宝"，提供社区信息、房产、招聘等本地服务功能。

- 每次编写代码前先阅读最新代码
- 对于要求和要实现的功能先进行思考和规划，选择最优的方案后再进行后续的操作

## 主要文件

- 入口文件：[main.ts](mdc:src/main.ts)
- 应用组件：[App.vue](mdc:src/App.vue)
- 页面配置：[pages.json](mdc:src/pages.json)

## 项目结构

- `src/pages`: 所有页面组件
- `src/components`: 通用组件
- `src/stores`: Pinia状态管理
- `src/utils`: 工具函数
- `src/styles`: 样式文件

## 技术栈

- 框架：Vue 3 + TypeScript + uni-app
- 状态管理：Pinia
- UI库：uni-ui、ThorUI
- CSS：UnoCSS + SCSS
- 网络请求：uni-network
- 分页组件：z-paging

