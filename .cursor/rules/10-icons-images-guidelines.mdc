---
description: 
globs: 
alwaysApply: true
---
# 图标与图像使用规范

本规范定义了项目中图标和图像的使用标准，以保持视觉一致性和优化性能。

## 图标系统

### 图标使用方式

本项目支持以下几种图标使用方式：

1. **UnoCSS图标**（推荐）
   - 项目配置了Carbon和Solar图标集
   - 无需额外请求，体积小且高效
   - 使用方式：添加对应类名

```html
<!-- Carbon图标 -->
<view class="i-carbon-home text-24px"></view>

<!-- Solar图标 -->
<view class="i-solar-heart-bold text-primary text-24px"></view>
```

2. **静态图片图标**
   - 存放于`static/icons`目录
   - 适用于特殊图标或无法通过图标库获取的图标
   - 使用`<image>`标签加载

```html
<image src="/static/icons/custom-icon.png" class="icon-24"></image>
```

### 图标尺寸规范

- **小图标**：32rpx (16px)，用于辅助说明
- **中图标**：40rpx (20px)，用于常规场景
- **大图标**：48rpx (24px)，用于强调或主导航

可使用以下尺寸类：
- `text-16px`、`text-20px`、`text-24px`

### 图标颜色

- 默认使用文本颜色(`$text-main`)
- 强调图标使用主色(`$primary`)
- 次要图标使用灰色(`$text-grey`)

```html
<!-- 主要图标 -->
<view class="i-carbon-star-filled text-primary text-24px"></view>

<!-- 次要图标 -->
<view class="i-carbon-information text-grey text-20px"></view>
```

## 图片使用

### 图片存放位置

- UI装饰性图片：`static/images/`
- 内容相关图片：通过API获取或上传

### 图片格式选择

- **照片和复杂图像**：使用JPG格式
- **需要透明度的图像**：使用PNG格式
- **简单图形和图标**：优先使用SVG或UnoCSS图标

### 图片尺寸优化

- 控制图片分辨率，避免过大
- 使用适当的压缩比例
- 使用适合的`mode`属性

```html
<!-- 方形图片 -->
<image src="/static/images/avatar.jpg" mode="aspectFill" class="avatar"></image>

<!-- 横向图片 -->
<image src="/static/images/banner.jpg" mode="widthFix" class="banner"></image>
```

### 图片加载处理

- 使用`<ImageLoader>`组件处理图片加载
- 设置占位图和加载失败图
- 实现懒加载提高性能

```vue
<template>
  <ImageLoader
    src="https://example.com/image.jpg"
    placeholder="/static/images/placeholder.png"
    error-image="/static/images/error.png"
    mode="aspectFill"
    :width="100"
    :height="100"
  />
</template>
```

## 常见图片组件样式

### 头像样式

```vue
<template>
  <!-- 圆形头像 -->
  <view class="avatar avatar-round">
    <image src="/static/images/avatar.jpg" mode="aspectFill"></image>
  </view>
  
  <!-- 方形头像 -->
  <view class="avatar avatar-square">
    <image src="/static/images/avatar.jpg" mode="aspectFill"></image>
  </view>
</template>

<style lang="scss">
.avatar {
  overflow: hidden;
  width: 80rpx;
  height: 80rpx;
  
  &-round {
    border-radius: 50%;
  }
  
  &-square {
    border-radius: $border-radius-base;
  }
  
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
```

### 图片列表/九宫格

```vue
<template>
  <view class="image-grid">
    <view 
      v-for="(item, index) in images" 
      :key="index" 
      class="image-grid-item"
    >
      <ImageLoader 
        :src="item" 
        mode="aspectFill"
      />
    </view>
  </view>
</template>

<style lang="scss">
.image-grid {
  display: flex;
  flex-wrap: wrap;
  
  &-item {
    width: calc(33.33% - 8rpx);
    height: 200rpx;
    margin: 4rpx;
    border-radius: $border-radius-base;
    overflow: hidden;
  }
}
</style>
```

### 轮播图片

```vue
<template>
  <swiper 
    class="banner-swiper" 
    :indicator-dots="true" 
    :autoplay="true" 
    :interval="3000"
    :circular="true"
  >
    <swiper-item v-for="(item, index) in banners" :key="index">
      <image 
        :src="item.image" 
        mode="aspectFill" 
        class="banner-image" 
        @tap="onBannerTap(item)"
      />
    </swiper-item>
  </swiper>
</template>

<style lang="scss">
.banner-swiper {
  width: 100%;
  height: 300rpx;
  
  .banner-image {
    width: 100%;
    height: 100%;
  }
}
</style>
```

遵循以上规范可确保图标和图像的使用保持一致性，同时优化应用性能。
