---
description: 
globs: 
alwaysApply: true
---
# 性能优化与代码规范

本规范定义了项目的性能优化策略和代码编写规范，以确保应用高效运行和代码质量。

## 性能优化

### 图片优化

- 使用**适合的图片格式**:
  - JPG: 照片和复杂图像
  - PNG: 需要透明度的图像
  - SVG: 图标和简单图形
- 使用**适当的图片尺寸**，避免过大图片
- 使用**图片懒加载**，仅在需要时加载
  - 使用项目中的`<ImageLoader>`组件
  - 适当使用`uni-image`的`lazy-load`属性

```vue
<template>
  <ImageLoader 
    src="/static/images/banner.jpg" 
    mode="aspectFill" 
    :width="750" 
    :height="300" 
  />
</template>
```

### 列表优化

- 使用**虚拟列表**处理长列表，避免一次性渲染所有数据
- 实现**分页加载**，使用`z-paging`组件
- 避免在列表项中放置大量复杂组件

```vue
<template>
  <z-paging ref="paging" v-model="dataList" @query="queryList">
    <template v-for="(item, index) in dataList" :key="index">
      <PostItem :item="item" />
    </template>
  </z-paging>
</template>
```

### 避免不必要的渲染

- 使用`computed`属性而非方法处理派生数据
- 对于不常变化的列表使用`v-once`
- 大型组件使用`<keep-alive>`缓存

```vue
<script setup>
const count = ref(0)
// 使用computed而非方法
const doubleCount = computed(() => count.value * 2)
</script>
```

### 数据请求优化

- 实现数据**缓存机制**，避免重复请求
- 使用**防抖**和**节流**处理频繁触发的事件
- 避免一次性请求大量数据

## 代码规范

### 组件设计原则

- **单一职责**：一个组件只做一件事
- **可组合性**：小组件可组合成复杂界面
- **可重用性**：抽象通用逻辑为独立组件
- **可测试性**：组件设计易于测试

### Vue组件结构

```vue
<script setup lang="ts">
// 导入顺序: 外部库 -> 内部组件/工具
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { http } from '@/utils/http'

// 状态管理
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const dataList = ref([])

// 计算属性
const isLogin = computed(() => !!userStore.user)

// 方法定义 - 使用箭头函数保持一致性
const fetchData = async () => {
  loading.value = true
  try {
    const res = await http.get('/api/data')
    dataList.value = res.data
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  fetchData()
})
</script>

<template>
  <!-- 主容器 -->
  <view class="container">
    <!-- 内容区域 -->
  </view>
</template>

<style lang="scss">
// 作用域样式，避免全局污染
.container {
  // 先写盒模型
  width: 100%;
  padding: 20rpx;
  
  // 再写定位
  position: relative;
  
  // 再写排版
  display: flex;
  flex-direction: column;
  
  // 最后写样式
  background-color: #fff;
  border-radius: $border-radius-base;
}
</style>
```

### TypeScript使用规范

- 为所有数据模型定义接口或类型
- 使用类型推断减少冗余代码
- 避免使用`any`类型，优先使用具体类型

```ts
// 定义接口
interface User {
  id: number
  username: string
  avatar?: string
}

// 使用类型
const user = ref<User | null>(null)
```

### 状态管理规范

- 本地组件状态使用`ref`/`reactive`
- 跨组件共享状态使用Pinia Store
- 复杂表单状态考虑使用第三方表单库

### 异步操作处理

- 使用`async/await`处理异步操作
- 始终处理异常情况
- 提供加载状态反馈

```ts
const submitForm = async () => {
  loading.value = true
  try {
    await http.post('/api/submit', form.value)
    uni.showToast({ title: '提交成功', icon: 'success' })
  } catch (error) {
    uni.showToast({ title: '提交失败', icon: 'none' })
  } finally {
    loading.value = false
  }
}
```

## 工程化实践

### 代码分割

- 使用动态导入减小主包体积
- 合理划分分包结构

### 依赖管理

- 使用pnpm作为包管理工具
- 避免安装不必要的依赖
- 定期更新依赖包

遵循上述规范可确保代码高质量、可维护且性能良好。
